"""
Event Repository Layer

Repository pattern implementation untuk Event entity.
Satu-satunya layer yang boleh berinteraksi langsung dengan database.
Menyediakan abstraksi untuk semua operasi CRUD dan query.
"""

from typing import List, Optional, Tuple
from sqlalchemy import and_, or_
from sqlalchemy.exc import SQLAlchemyError
from app.extensions import db
from app.models import Event, EventStatus


class EventRepository:
    """
    Repository class untuk Event entity.
    Mengimplementasikan semua operasi database untuk Event.
    """
    
    @staticmethod
    def create(event: Event) -> Event:
        """
        Membuat event baru di database.
        
        Args:
            event (Event): Instance Event yang akan disimpan
            
        Returns:
            Event: Event yang telah disimpan dengan ID
            
        Raises:
            SQLAlchemyError: Jika terjadi error database
        """
        try:
            db.session.add(event)
            db.session.commit()
            return event
        except SQLAlchemyError as e:
            db.session.rollback()
            raise e
    
    @staticmethod
    def get_by_id(event_id: int) -> Optional[Event]:
        """
        Mengambil event berdasarkan ID.
        
        Args:
            event_id (int): ID event yang dicari
            
        Returns:
            Optional[Event]: Event jika ditemukan, None jika tidak
        """
        return Event.query.get(event_id)
    
    @staticmethod
    def get_all(page: int = 1, per_page: int = 20, status: Optional[str] = None) -> Tuple[List[Event], dict]:
        """
        Mengambil semua event dengan pagination dan filter.
        
        Args:
            page (int): Nomor halaman (default: 1)
            per_page (int): Jumlah item per halaman (default: 20)
            status (Optional[str]): Filter berdasarkan status
            
        Returns:
            Tuple[List[Event], dict]: List event dan metadata pagination
        """
        query = Event.query
        
        # Filter berdasarkan status jika diberikan
        if status:
            try:
                status_enum = EventStatus(status)
                query = query.filter(Event.status == status_enum)
            except ValueError:
                # Status tidak valid, return empty result
                return [], {
                    'page': page,
                    'per_page': per_page,
                    'total': 0,
                    'pages': 0,
                    'has_next': False,
                    'has_prev': False
                }
        
        # Order by event_date ascending (upcoming events first)
        query = query.order_by(Event.event_date.asc())
        
        # Execute pagination
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        return pagination.items, {
            'page': pagination.page,
            'per_page': pagination.per_page,
            'total': pagination.total,
            'pages': pagination.pages,
            'has_next': pagination.has_next,
            'has_prev': pagination.has_prev
        }
    
    @staticmethod
    def update(event: Event) -> Event:
        """
        Update event di database.
        
        Args:
            event (Event): Instance Event yang akan diupdate
            
        Returns:
            Event: Event yang telah diupdate
            
        Raises:
            SQLAlchemyError: Jika terjadi error database
        """
        try:
            db.session.commit()
            return event
        except SQLAlchemyError as e:
            db.session.rollback()
            raise e
    
    @staticmethod
    def delete(event: Event) -> bool:
        """
        Menghapus event dari database.
        
        Args:
            event (Event): Instance Event yang akan dihapus
            
        Returns:
            bool: True jika berhasil dihapus
            
        Raises:
            SQLAlchemyError: Jika terjadi error database
        """
        try:
            db.session.delete(event)
            db.session.commit()
            return True
        except SQLAlchemyError as e:
            db.session.rollback()
            raise e
    
    @staticmethod
    def search(query: str, page: int = 1, per_page: int = 20) -> Tuple[List[Event], dict]:
        """
        Mencari event berdasarkan nama atau lokasi.
        
        Args:
            query (str): Query pencarian
            page (int): Nomor halaman
            per_page (int): Jumlah item per halaman
            
        Returns:
            Tuple[List[Event], dict]: List event dan metadata pagination
        """
        search_filter = or_(
            Event.name.ilike(f'%{query}%'),
            Event.location.ilike(f'%{query}%')
        )
        
        pagination = Event.query.filter(search_filter)\
            .order_by(Event.event_date.asc())\
            .paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )
        
        return pagination.items, {
            'page': pagination.page,
            'per_page': pagination.per_page,
            'total': pagination.total,
            'pages': pagination.pages,
            'has_next': pagination.has_next,
            'has_prev': pagination.has_prev
        }
    
    @staticmethod
    def get_by_status(status: EventStatus, page: int = 1, per_page: int = 20) -> Tuple[List[Event], dict]:
        """
        Mengambil event berdasarkan status tertentu.
        
        Args:
            status (EventStatus): Status event yang dicari
            page (int): Nomor halaman
            per_page (int): Jumlah item per halaman
            
        Returns:
            Tuple[List[Event], dict]: List event dan metadata pagination
        """
        pagination = Event.query.filter(Event.status == status)\
            .order_by(Event.event_date.asc())\
            .paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )
        
        return pagination.items, {
            'page': pagination.page,
            'per_page': pagination.per_page,
            'total': pagination.total,
            'pages': pagination.pages,
            'has_next': pagination.has_next,
            'has_prev': pagination.has_prev
        }
