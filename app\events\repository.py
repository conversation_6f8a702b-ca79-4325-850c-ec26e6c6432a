"""
Event Repository Layer

Repository pattern implementation untuk Event entity menggunakan Supabase.
Satu-satunya layer yang boleh berinteraksi langsung dengan database.
Menyediakan abstraksi untuk semua operasi CRUD dan query.
"""

from typing import List, Optional, Tuple, Dict, Any
from app.models import Event, EventStatus
from app.extensions import get_supabase_client


class EventRepository:
    """
    Repository class untuk Event entity menggunakan Supabase.
    Mengimplementasikan semua operasi database untuk Event.
    """

    TABLE_NAME = "events"

    @staticmethod
    def create(event: Event) -> Event:
        """
        Membuat event baru di Supabase.

        Args:
            event (Event): Instance Event yang akan disimpan

        Returns:
            Event: Event yang telah disimpan dengan ID

        Raises:
            Exception: Jika terjadi error database
        """
        try:
            # Get Supabase client
            supabase = get_supabase_client()

            # Convert event ke format Supabase
            event_data = event.to_supabase_dict()

            # Insert ke Supabase
            result = supabase.table(EventRepository.TABLE_NAME).insert(event_data).execute()

            if result.data:
                # Update event dengan data dari database (termasuk ID)
                created_event = Event.from_dict(result.data[0])
                return created_event
            else:
                raise Exception("Failed to create event")

        except Exception as e:
            raise e
    
    @staticmethod
    def get_by_id(event_id: int) -> Optional[Event]:
        """
        Mengambil event berdasarkan ID dari Supabase.

        Args:
            event_id (int): ID event yang dicari

        Returns:
            Optional[Event]: Event jika ditemukan, None jika tidak
        """
        try:
            # Get Supabase client
            supabase = get_supabase_client()

            result = supabase.table(EventRepository.TABLE_NAME)\
                .select("*")\
                .eq("id", event_id)\
                .execute()

            if result.data:
                return Event.from_dict(result.data[0])
            return None

        except Exception:
            return None
    
    @staticmethod
    def get_all(page: int = 1, per_page: int = 20, status: Optional[str] = None) -> Tuple[List[Event], dict]:
        """
        Mengambil semua event dengan pagination dan filter dari Supabase.

        Args:
            page (int): Nomor halaman (default: 1)
            per_page (int): Jumlah item per halaman (default: 20)
            status (Optional[str]): Filter berdasarkan status

        Returns:
            Tuple[List[Event], dict]: List event dan metadata pagination
        """
        try:
            # Get Supabase client
            supabase = get_supabase_client()

            # Calculate offset untuk pagination
            offset = (page - 1) * per_page

            # Build query
            query = supabase.table(EventRepository.TABLE_NAME).select("*")

            # Filter berdasarkan status jika diberikan
            if status:
                try:
                    # Validate status
                    EventStatus(status)
                    query = query.eq("status", status)
                except ValueError:
                    # Status tidak valid, return empty result
                    return [], {
                        'page': page,
                        'per_page': per_page,
                        'total': 0,
                        'pages': 0,
                        'has_next': False,
                        'has_prev': False
                    }

            # Get total count untuk pagination metadata
            count_result = supabase.table(EventRepository.TABLE_NAME).select("id", count="exact")
            if status:
                count_result = count_result.eq("status", status)
            count_result = count_result.execute()
            total = count_result.count if count_result.count else 0

            # Execute query dengan pagination dan ordering
            result = query.order("event_date", desc=False)\
                .range(offset, offset + per_page - 1)\
                .execute()

            # Convert hasil ke Event objects
            events = [Event.from_dict(item) for item in result.data] if result.data else []

            # Calculate pagination metadata
            pages = (total + per_page - 1) // per_page
            has_next = page < pages
            has_prev = page > 1

            return events, {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': pages,
                'has_next': has_next,
                'has_prev': has_prev
            }

        except Exception as e:
            # Return empty result jika terjadi error
            return [], {
                'page': page,
                'per_page': per_page,
                'total': 0,
                'pages': 0,
                'has_next': False,
                'has_prev': False
            }
    
    @staticmethod
    def update(event: Event) -> Event:
        """
        Update event di Supabase.

        Args:
            event (Event): Instance Event yang akan diupdate

        Returns:
            Event: Event yang telah diupdate

        Raises:
            Exception: Jika terjadi error database
        """
        try:
            # Get Supabase client
            supabase = get_supabase_client()

            # Convert event ke format Supabase (tanpa created_at)
            event_data = event.to_supabase_dict()
            # Remove created_at karena ini update operation
            if 'created_at' in event_data:
                del event_data['created_at']

            # Update di Supabase
            result = supabase.table(EventRepository.TABLE_NAME)\
                .update(event_data)\
                .eq("id", event.id)\
                .execute()

            if result.data:
                return Event.from_dict(result.data[0])
            else:
                raise Exception("Failed to update event")

        except Exception as e:
            raise e
    
    @staticmethod
    def delete(event: Event) -> bool:
        """
        Menghapus event dari Supabase.

        Args:
            event (Event): Instance Event yang akan dihapus

        Returns:
            bool: True jika berhasil dihapus

        Raises:
            Exception: Jika terjadi error database
        """
        try:
            # Get Supabase client
            supabase = get_supabase_client()

            result = supabase.table(EventRepository.TABLE_NAME)\
                .delete()\
                .eq("id", event.id)\
                .execute()

            # Supabase delete akan return empty data jika berhasil
            return True

        except Exception as e:
            raise e
    
    @staticmethod
    def search(query: str, page: int = 1, per_page: int = 20) -> Tuple[List[Event], dict]:
        """
        Mencari event berdasarkan nama atau lokasi di Supabase.

        Args:
            query (str): Query pencarian
            page (int): Nomor halaman
            per_page (int): Jumlah item per halaman

        Returns:
            Tuple[List[Event], dict]: List event dan metadata pagination
        """
        try:
            # Get Supabase client
            supabase = get_supabase_client()

            # Calculate offset untuk pagination
            offset = (page - 1) * per_page

            # Supabase search menggunakan ilike untuk case-insensitive search
            # Kita akan search di name dan location
            name_search = supabase.table(EventRepository.TABLE_NAME)\
                .select("*")\
                .ilike("name", f"%{query}%")

            location_search = supabase.table(EventRepository.TABLE_NAME)\
                .select("*")\
                .ilike("location", f"%{query}%")

            # Execute both searches
            name_result = name_search.execute()
            location_result = location_search.execute()

            # Combine results dan remove duplicates
            all_results = []
            seen_ids = set()

            for item in (name_result.data or []) + (location_result.data or []):
                if item['id'] not in seen_ids:
                    all_results.append(item)
                    seen_ids.add(item['id'])

            # Sort by event_date
            all_results.sort(key=lambda x: x['event_date'])

            # Apply pagination
            total = len(all_results)
            paginated_results = all_results[offset:offset + per_page]

            # Convert ke Event objects
            events = [Event.from_dict(item) for item in paginated_results]

            # Calculate pagination metadata
            pages = (total + per_page - 1) // per_page if total > 0 else 0
            has_next = page < pages
            has_prev = page > 1

            return events, {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': pages,
                'has_next': has_next,
                'has_prev': has_prev
            }

        except Exception as e:
            # Return empty result jika terjadi error
            return [], {
                'page': page,
                'per_page': per_page,
                'total': 0,
                'pages': 0,
                'has_next': False,
                'has_prev': False
            }
    
    @staticmethod
    def get_by_status(status: EventStatus, page: int = 1, per_page: int = 20) -> Tuple[List[Event], dict]:
        """
        Mengambil event berdasarkan status tertentu dari Supabase.

        Args:
            status (EventStatus): Status event yang dicari
            page (int): Nomor halaman
            per_page (int): Jumlah item per halaman

        Returns:
            Tuple[List[Event], dict]: List event dan metadata pagination
        """
        try:
            # Get Supabase client
            supabase = get_supabase_client()

            # Calculate offset untuk pagination
            offset = (page - 1) * per_page

            # Get total count untuk pagination metadata
            count_result = supabase.table(EventRepository.TABLE_NAME)\
                .select("id", count="exact")\
                .eq("status", status.value)\
                .execute()
            total = count_result.count if count_result.count else 0

            # Execute query dengan pagination dan ordering
            result = supabase.table(EventRepository.TABLE_NAME)\
                .select("*")\
                .eq("status", status.value)\
                .order("event_date", desc=False)\
                .range(offset, offset + per_page - 1)\
                .execute()

            # Convert hasil ke Event objects
            events = [Event.from_dict(item) for item in result.data] if result.data else []

            # Calculate pagination metadata
            pages = (total + per_page - 1) // per_page if total > 0 else 0
            has_next = page < pages
            has_prev = page > 1

            return events, {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': pages,
                'has_next': has_next,
                'has_prev': has_prev
            }

        except Exception as e:
            # Return empty result jika terjadi error
            return [], {
                'page': page,
                'per_page': per_page,
                'total': 0,
                'pages': 0,
                'has_next': False,
                'has_prev': False
            }
