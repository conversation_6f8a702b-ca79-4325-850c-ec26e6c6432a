"""
Database Models untuk Event Management API

Definisi model SQLAlchemy untuk semua entitas dalam sistem.
Menggunakan best practices untuk database design dan ORM relationships.
"""

from datetime import datetime
from enum import Enum
from app.extensions import db


class EventStatus(Enum):
    """
    Enum untuk status event yang memungkinkan.
    Memberikan type safety dan konsistensi data.
    """
    DRAFT = "draft"
    PUBLISHED = "published"
    ONGOING = "ongoing"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class Event(db.Model):
    """
    Model untuk Event entity.
    
    Attributes:
        id (int): Primary key, auto-increment
        name (str): Nama event, required, max 200 karakter
        event_date (datetime): Tanggal dan waktu event
        location (str): Lokasi event, max 300 karakter
        status (EventStatus): Status event menggunakan enum
        created_at (datetime): Timestamp pembuatan record
        updated_at (datetime): Timestamp update terakhir
    """
    
    __tablename__ = 'events'
    
    # Primary Key
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    
    # Event Information
    name = db.Column(db.String(200), nullable=False, index=True)
    event_date = db.Column(db.DateTime, nullable=False, index=True)
    location = db.Column(db.String(300), nullable=True)
    
    # Status menggunakan Enum untuk data consistency
    status = db.Column(db.Enum(EventStatus), nullable=False, default=EventStatus.DRAFT, index=True)
    
    # Timestamps
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, name, event_date, location=None, status=EventStatus.DRAFT):
        """
        Constructor untuk Event model.
        
        Args:
            name (str): Nama event
            event_date (datetime): Tanggal event
            location (str, optional): Lokasi event
            status (EventStatus, optional): Status event, default DRAFT
        """
        self.name = name
        self.event_date = event_date
        self.location = location
        self.status = status
    
    def __repr__(self):
        """String representation untuk debugging"""
        return f'<Event {self.id}: {self.name} ({self.status.value})>'
    
    def to_dict(self):
        """
        Convert model instance ke dictionary untuk JSON serialization.
        
        Returns:
            dict: Dictionary representation dari event
        """
        return {
            'id': self.id,
            'name': self.name,
            'event_date': self.event_date.isoformat() if self.event_date else None,
            'location': self.location,
            'status': self.status.value,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def from_dict(cls, data):
        """
        Create Event instance dari dictionary data.
        
        Args:
            data (dict): Dictionary dengan event data
            
        Returns:
            Event: Instance Event yang baru
        """
        # Parse event_date jika berupa string
        event_date = data.get('event_date')
        if isinstance(event_date, str):
            event_date = datetime.fromisoformat(event_date.replace('Z', '+00:00'))
        
        # Parse status jika berupa string
        status = data.get('status', EventStatus.DRAFT)
        if isinstance(status, str):
            status = EventStatus(status)
        
        return cls(
            name=data.get('name'),
            event_date=event_date,
            location=data.get('location'),
            status=status
        )
    
    def update_from_dict(self, data):
        """
        Update instance dengan data dari dictionary.
        
        Args:
            data (dict): Dictionary dengan data update
        """
        if 'name' in data:
            self.name = data['name']
        
        if 'event_date' in data:
            event_date = data['event_date']
            if isinstance(event_date, str):
                event_date = datetime.fromisoformat(event_date.replace('Z', '+00:00'))
            self.event_date = event_date
        
        if 'location' in data:
            self.location = data['location']
        
        if 'status' in data:
            status = data['status']
            if isinstance(status, str):
                status = EventStatus(status)
            self.status = status
        
        # Update timestamp
        self.updated_at = datetime.utcnow()
