"""
Data Models untuk Event Management API

Definisi model data untuk semua entitas dalam sistem.
Menggunakan Pydantic untuk validation dan Supabase untuk storage.
"""

from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any
from pydantic import BaseModel, validator


class EventStatus(Enum):
    """
    Enum untuk status event yang memungkinkan.
    Memberikan type safety dan konsistensi data.
    """
    DRAFT = "draft"
    PUBLISHED = "published"
    ONGOING = "ongoing"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class Event:
    """
    Model untuk Event entity yang akan disimpan di Supabase.

    Attributes:
        id (int): Primary key, auto-increment
        name (str): Nama event, required, max 200 karakter
        event_date (datetime): Tanggal dan waktu event
        location (str): Lokasi event, max 300 karakter
        status (EventStatus): Status event menggunakan enum
        created_at (datetime): Timestamp pembuatan record
        updated_at (datetime): Timestamp update terakhir
    """

    def __init__(self, name: str, event_date: datetime, location: Optional[str] = None,
                 status: EventStatus = EventStatus.DRAFT, id: Optional[int] = None,
                 created_at: Optional[datetime] = None, updated_at: Optional[datetime] = None):
        """
        Constructor untuk Event model.

        Args:
            name (str): Nama event
            event_date (datetime): Tanggal event
            location (str, optional): Lokasi event
            status (EventStatus, optional): Status event, default DRAFT
            id (int, optional): ID event (untuk data dari database)
            created_at (datetime, optional): Timestamp created
            updated_at (datetime, optional): Timestamp updated
        """
        self.id = id
        self.name = name
        self.event_date = event_date
        self.location = location
        self.status = status
        self.created_at = created_at or datetime.utcnow()
        self.updated_at = updated_at or datetime.utcnow()

    def __repr__(self):
        """String representation untuk debugging"""
        return f'<Event {self.id}: {self.name} ({self.status.value})>'

    def to_dict(self):
        """
        Convert model instance ke dictionary untuk JSON serialization.

        Returns:
            dict: Dictionary representation dari event
        """
        return {
            'id': self.id,
            'name': self.name,
            'event_date': self.event_date.isoformat() if self.event_date else None,
            'location': self.location,
            'status': self.status.value,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def to_supabase_dict(self):
        """
        Convert model instance ke dictionary untuk Supabase insert/update.

        Returns:
            dict: Dictionary untuk Supabase operations
        """
        data = {
            'name': self.name,
            'event_date': self.event_date.isoformat() if self.event_date else None,
            'location': self.location,
            'status': self.status.value,
            'updated_at': self.updated_at.isoformat()
        }

        # Include created_at only for new records
        if not self.id:
            data['created_at'] = self.created_at.isoformat()

        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Event':
        """
        Create Event instance dari dictionary data.

        Args:
            data (dict): Dictionary dengan event data

        Returns:
            Event: Instance Event yang baru
        """
        # Parse event_date jika berupa string
        event_date = data.get('event_date')
        if isinstance(event_date, str):
            # Handle both ISO format and Supabase timestamp format
            event_date = datetime.fromisoformat(event_date.replace('Z', '+00:00'))

        # Parse created_at dan updated_at
        created_at = data.get('created_at')
        if isinstance(created_at, str):
            created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))

        updated_at = data.get('updated_at')
        if isinstance(updated_at, str):
            updated_at = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))

        # Parse status jika berupa string
        status = data.get('status', EventStatus.DRAFT)
        if isinstance(status, str):
            status = EventStatus(status)

        return cls(
            id=data.get('id'),
            name=data.get('name'),
            event_date=event_date,
            location=data.get('location'),
            status=status,
            created_at=created_at,
            updated_at=updated_at
        )

    def update_from_dict(self, data: Dict[str, Any]):
        """
        Update instance dengan data dari dictionary.

        Args:
            data (dict): Dictionary dengan data update
        """
        if 'name' in data:
            self.name = data['name']

        if 'event_date' in data:
            event_date = data['event_date']
            if isinstance(event_date, str):
                event_date = datetime.fromisoformat(event_date.replace('Z', '+00:00'))
            self.event_date = event_date

        if 'location' in data:
            self.location = data['location']

        if 'status' in data:
            status = data['status']
            if isinstance(status, str):
                status = EventStatus(status)
            self.status = status

        # Update timestamp
        self.updated_at = datetime.utcnow()
