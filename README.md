# Event Management API

Backend REST API untuk Dasbor Manajemen Event yang di<PERSON>un dengan <PERSON>lask, SQLAlchemy, dan <PERSON> (Supabase).

## 🏗️ Arsitektur

Aplikasi ini menggunakan **Application Factory Pattern** dengan **Layered Architecture**:

```
┌─────────────────┐
│   Frontend      │ (Vue.js/Next.js/Svelte)
│   (Terpisah)    │
└─────────────────┘
         │
         ▼
┌─────────────────┐
│   Routes Layer  │ (HTTP Endpoints)
│   routes.py     │
└─────────────────┘
         │
         ▼
┌─────────────────┐
│  Service Layer  │ (Business Logic)
│   service.py    │
└─────────────────┘
         │
         ▼
┌─────────────────┐
│Repository Layer │ (Data Access)
│ repository.py   │
└─────────────────┘
         │
         ▼
┌─────────────────┐
│   Database      │ (Supabase PostgreSQL)
│   models.py     │
└─────────────────┘
```

## 📁 Struktur Proyek

```
flask-api/
├── app/                    # Application package
│   ├── __init__.py        # Application factory
│   ├── config.py          # Configuration management
│   ├── extensions.py      # Flask extensions
│   ├── models.py          # SQLAlchemy models
│   └── events/            # Events module
│       ├── __init__.py
│       ├── repository.py  # Data access layer
│       ├── service.py     # Business logic layer
│       └── routes.py      # HTTP routes (Blueprint)
├── api/
│   └── index.py           # Entry point (Vercel)
├── requirements.txt       # Dependencies
├── .env.example          # Environment template
└── README.md
```

## 🚀 Quick Start

### 1. Setup Environment

```bash
# Clone repository
git clone <repository-url>
cd flask-api

# Install dependencies
pip install -r requirements.txt

# Setup environment variables
cp .env.example .env
# Edit .env dengan kredensial Supabase Anda
```

### 2. Konfigurasi Database

Edit file `.env`:

```env
DATABASE_URL=******************************************************/your-database-name
SECRET_KEY=your-secret-key-here
FLASK_ENV=development
FLASK_DEBUG=True
```

### 3. Jalankan Aplikasi

```bash
# Development
python api/index.py

# Atau dengan Flask CLI
export FLASK_APP=api/index.py
flask run
```

API akan tersedia di `http://localhost:5000`

## 📚 API Documentation

### Base URL

```
http://localhost:5000/api/v1
```

### Endpoints

#### Health Check

```http
GET /health
```

#### Events

| Method | Endpoint       | Description                    |
| ------ | -------------- | ------------------------------ |
| POST   | `/events`      | Membuat event baru             |
| GET    | `/events`      | Mengambil daftar event         |
| GET    | `/events/{id}` | Mengambil event berdasarkan ID |
| PUT    | `/events/{id}` | Update event                   |
| DELETE | `/events/{id}` | Menghapus event                |

### Event Model

```json
{
  "id": 1,
  "name": "Tech Conference 2024",
  "event_date": "2024-12-15T10:00:00",
  "location": "Jakarta Convention Center",
  "status": "published",
  "created_at": "2024-01-01T00:00:00",
  "updated_at": "2024-01-01T00:00:00"
}
```

### Status Values

- `draft` - Event masih draft
- `published` - Event sudah dipublikasi
- `ongoing` - Event sedang berlangsung
- `completed` - Event sudah selesai
- `cancelled` - Event dibatalkan

## 🧪 API Usage Examples

### Membuat Event Baru

```bash
curl -X POST http://localhost:5000/api/v1/events \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Tech Conference 2024",
    "event_date": "2024-12-15T10:00:00",
    "location": "Jakarta Convention Center",
    "status": "draft"
  }'
```

### Mengambil Daftar Event

```bash
# Semua event
curl http://localhost:5000/api/v1/events

# Dengan pagination
curl "http://localhost:5000/api/v1/events?page=1&per_page=10"

# Filter berdasarkan status
curl "http://localhost:5000/api/v1/events?status=published"

# Pencarian
curl "http://localhost:5000/api/v1/events?search=conference"
```

### Update Event

```bash
curl -X PUT http://localhost:5000/api/v1/events/1 \
  -H "Content-Type: application/json" \
  -d '{
    "status": "published"
  }'
```

## 🔧 Development

### Database Migration

```bash
# Buat database tables
python -c "from api.index import app; from app.extensions import db; app.app_context().push(); db.create_all()"
```

### Testing

```bash
# Install testing dependencies
pip install pytest pytest-flask

# Run tests (jika ada)
pytest
```

## 🚀 Deployment

### Vercel Deployment

1. Push code ke GitHub
2. Connect repository ke Vercel
3. Set environment variables di Vercel dashboard
4. Deploy

### Environment Variables untuk Production

```env
DATABASE_URL=your-supabase-production-url
SECRET_KEY=your-production-secret-key
FLASK_ENV=production
FLASK_DEBUG=False
```

## 🏛️ Arsitektur Decisions

### Mengapa Layered Architecture?

1. **Separation of Concerns**: Setiap layer memiliki tanggung jawab yang jelas
2. **Testability**: Mudah untuk unit testing setiap layer
3. **Maintainability**: Perubahan di satu layer tidak mempengaruhi layer lain
4. **Scalability**: Mudah untuk menambah fitur baru

### Mengapa Repository Pattern?

1. **Data Access Abstraction**: Memisahkan business logic dari database operations
2. **Testing**: Mudah untuk mock data layer dalam testing
3. **Database Agnostic**: Mudah untuk mengganti database di masa depan

## 🤝 Contributing

1. Fork repository
2. Create feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request
