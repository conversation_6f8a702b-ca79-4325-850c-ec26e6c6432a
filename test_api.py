#!/usr/bin/env python3
"""
Test script untuk Event Management API

Script ini menguji semua endpoint API untuk memastikan semuanya berfungsi dengan baik.
Pastikan server su<PERSON> be<PERSON><PERSON><PERSON> di http://localhost:5000 sebelum menjalankan script ini.
"""

import requests
import json
from datetime import datetime, timedelta

BASE_URL = "http://localhost:3000"

def test_health_check():
    """Test health check endpoint"""
    print("🔍 Testing health check...")
    response = requests.get(f"{BASE_URL}/health")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()

def test_create_event():
    """Test create event endpoint"""
    print("🔍 Testing create event...")
    
    # Event data
    event_data = {
        "name": "Test Event API",
        "event_date": (datetime.now() + timedelta(days=30)).isoformat(),
        "location": "Test Location",
        "status": "draft"
    }
    
    response = requests.post(
        f"{BASE_URL}/api/v1/events",
        json=event_data,
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    
    if response.status_code == 201:
        return response.json()["data"]["id"]
    return None

def test_get_events():
    """Test get all events endpoint"""
    print("🔍 Testing get all events...")
    response = requests.get(f"{BASE_URL}/api/v1/events")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()

def test_get_event_by_id(event_id):
    """Test get event by ID endpoint"""
    if not event_id:
        print("⚠️ Skipping get event by ID test (no event ID)")
        return
        
    print(f"🔍 Testing get event by ID: {event_id}")
    response = requests.get(f"{BASE_URL}/api/v1/events/{event_id}")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()

def test_update_event(event_id):
    """Test update event endpoint"""
    if not event_id:
        print("⚠️ Skipping update event test (no event ID)")
        return
        
    print(f"🔍 Testing update event: {event_id}")
    
    update_data = {
        "status": "published",
        "location": "Updated Location"
    }
    
    response = requests.put(
        f"{BASE_URL}/api/v1/events/{event_id}",
        json=update_data,
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()

def test_search_events():
    """Test search events endpoint"""
    print("🔍 Testing search events...")
    response = requests.get(f"{BASE_URL}/api/v1/events?search=test")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()

def test_filter_events():
    """Test filter events by status"""
    print("🔍 Testing filter events by status...")
    response = requests.get(f"{BASE_URL}/api/v1/events?status=published")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()

def test_delete_event(event_id):
    """Test delete event endpoint"""
    if not event_id:
        print("⚠️ Skipping delete event test (no event ID)")
        return
        
    print(f"🔍 Testing delete event: {event_id}")
    response = requests.delete(f"{BASE_URL}/api/v1/events/{event_id}")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()

def main():
    """Run all tests"""
    print("🚀 Starting Event Management API Tests")
    print("=" * 50)
    
    try:
        # Test health check
        test_health_check()
        
        # Test create event
        event_id = test_create_event()
        print()
        
        # Test get all events
        test_get_events()
        
        # Test get event by ID
        test_get_event_by_id(event_id)
        
        # Test update event
        test_update_event(event_id)
        
        # Test search events
        test_search_events()
        
        # Test filter events
        test_filter_events()
        
        # Test delete event (uncomment jika ingin menghapus test event)
        # test_delete_event(event_id)
        
        print("✅ All tests completed!")
        
    except requests.exceptions.ConnectionError:
        print("❌ Error: Could not connect to the API server.")
        print("Make sure the server is running at http://localhost:3000")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
