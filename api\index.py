"""
Event Management API - Entry Point

Entry point untuk Vercel deployment dengan Application Factory pattern.
"""

import os
from app import create_app
from app.config import config

# Determine environment
config_name = os.environ.get('FLASK_ENV', 'development')
app = create_app(config.get(config_name, config['default']))

# Create database tables jika belum ada
with app.app_context():
    from app.extensions import db
    db.create_all()

if __name__ == '__main__':
    app.run(debug=app.config.get('DEBUG', False))