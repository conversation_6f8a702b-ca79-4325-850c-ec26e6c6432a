"""
Event Management API - Entry Point

Entry point untuk Vercel deployment dengan Application Factory pattern.
Menggunakan Supabase sebagai backend database.
"""

import os
from app import create_app
from app.config import config

# Determine environment
config_name = os.environ.get('FLASK_ENV', 'development')
app = create_app(config.get(config_name, config['default']))

if __name__ == '__main__':
    app.run(debug=app.config.get('DEBUG', False))