"""
Flask Extensions Initialization

Centralized initialization untuk Flask extensions yang digunakan dalam aplikasi.
Menggunakan Supabase client untuk database operations.
"""

import os
from supabase import create_client, Client

# Supabase client initialization
def get_supabase_client() -> Client:
    """
    Initialize dan return Supabase client.

    Returns:
        Client: Supabase client instance
    """
    url: str = os.environ.get("SUPABASE_URL")
    key: str = os.environ.get("SUPABASE_KEY")

    if not url or not key:
        raise ValueError("SUPABASE_URL and SUPABASE_KEY environment variables are required")

    return create_client(url, key)

# Global supabase client instance
supabase: Client = None

def init_supabase():
    """Initialize supabase client"""
    global supabase
    supabase = get_supabase_client()
