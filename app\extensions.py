"""
Flask Extensions Initialization

Centralized initialization untuk Flask extensions yang digunakan dalam aplikasi.
Menggunakan Supabase client untuk database operations dengan lazy loading.
"""

import os
from typing import Optional
from supabase import create_client, Client

# Global supabase client instance
_supabase_client: Optional[Client] = None

def get_supabase_client() -> Client:
    """
    Get atau initialize Supabase client dengan lazy loading.

    Returns:
        Client: Supabase client instance

    Raises:
        ValueError: Jika environment variables tidak ditemukan
    """
    global _supabase_client

    if _supabase_client is None:
        url: str = os.environ.get("SUPABASE_URL")
        key: str = os.environ.get("SUPABASE_KEY")

        if not url or not key:
            raise ValueError("SUPABASE_URL and SUPABASE_KEY environment variables are required")

        try:
            # Create client dengan konfigurasi sederhana
            _supabase_client = create_client(url, key)
        except Exception as e:
            # Log error tapi jangan crash aplikasi
            print(f"Warning: Failed to initialize Supabase client: {str(e)}")
            # Untuk development, kita bisa return None dan handle di repository
            raise Exception(f"Failed to initialize Supabase client: {str(e)}")

    return _supabase_client

def init_supabase():
    """
    Initialize supabase client (optional, untuk pre-loading).
    Client akan di-initialize secara lazy jika tidak dipanggil.
    """
    try:
        get_supabase_client()
        return True
    except Exception as e:
        print(f"Warning: Failed to initialize Supabase client: {str(e)}")
        return False
