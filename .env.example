# Database Configuration
# Supabase PostgreSQL Database URL
# Format: postgresql://username:password@host:port/database
DATABASE_URL=******************************************************/your-database-name

# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=True
SECRET_KEY=your-secret-key-here

# API Configuration
API_VERSION=v1

# CORS Configuration (untuk frontend development)
CORS_ORIGINS=http://localhost:3000,http://localhost:8080,http://localhost:5173
