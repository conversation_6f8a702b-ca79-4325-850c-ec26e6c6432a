"""
Event Service Layer

Business logic layer untuk Event operations.
Mengimplementasikan validasi, transformasi data, dan orchestration.
Bertindak sebagai perantara antara Routes dan Repository.
"""

from datetime import datetime
from typing import List, Optional, Tuple, Dict, Any
from app.models import Event, EventStatus
from app.events.repository import EventRepository


class EventService:
    """
    Service class untuk Event business logic.
    Mengelola validasi, transformasi, dan orchestration operasi Event.
    """
    
    @staticmethod
    def create_event(data: Dict[str, Any]) -> Tuple[Optional[Event], Optional[str]]:
        """
        Membuat event baru dengan validasi business logic.
        
        Args:
            data (Dict[str, Any]): Data event dari request
            
        Returns:
            Tuple[Optional[Event], Optional[str]]: (Event, error_message)
        """
        # Validasi required fields
        validation_error = EventService._validate_event_data(data)
        if validation_error:
            return None, validation_error
        
        try:
            # Create Event instance dari data
            event = Event.from_dict(data)
            
            # Business logic validations
            if event.event_date < datetime.utcnow():
                return None, "Event date cannot be in the past"
            
            # Save ke database melalui repository
            created_event = EventRepository.create(event)
            return created_event, None
            
        except ValueError as e:
            return None, f"Invalid data format: {str(e)}"
        except Exception as e:
            return None, f"Database error: {str(e)}"
    
    @staticmethod
    def get_event_by_id(event_id: int) -> Optional[Event]:
        """
        Mengambil event berdasarkan ID.
        
        Args:
            event_id (int): ID event
            
        Returns:
            Optional[Event]: Event jika ditemukan
        """
        if not isinstance(event_id, int) or event_id <= 0:
            return None
        
        return EventRepository.get_by_id(event_id)
    
    @staticmethod
    def get_all_events(page: int = 1, per_page: int = 20, status: Optional[str] = None) -> Tuple[List[Event], Dict[str, Any]]:
        """
        Mengambil semua event dengan pagination dan filter.
        
        Args:
            page (int): Nomor halaman
            per_page (int): Jumlah item per halaman
            status (Optional[str]): Filter status
            
        Returns:
            Tuple[List[Event], Dict[str, Any]]: (events, pagination_info)
        """
        # Validasi pagination parameters
        page = max(1, page)
        per_page = min(max(1, per_page), 100)  # Max 100 items per page
        
        return EventRepository.get_all(page, per_page, status)
    
    @staticmethod
    def update_event(event_id: int, data: Dict[str, Any]) -> Tuple[Optional[Event], Optional[str]]:
        """
        Update event dengan validasi business logic.
        
        Args:
            event_id (int): ID event yang akan diupdate
            data (Dict[str, Any]): Data update
            
        Returns:
            Tuple[Optional[Event], Optional[str]]: (Event, error_message)
        """
        # Cari event yang akan diupdate
        event = EventRepository.get_by_id(event_id)
        if not event:
            return None, "Event not found"
        
        # Validasi data update
        validation_error = EventService._validate_event_data(data, is_update=True)
        if validation_error:
            return None, validation_error
        
        try:
            # Business logic validations untuk update
            if 'event_date' in data:
                event_date = data['event_date']
                if isinstance(event_date, str):
                    event_date = datetime.fromisoformat(event_date.replace('Z', '+00:00'))
                
                # Tidak boleh mengubah tanggal event yang sudah berlalu
                if event_date < datetime.utcnow():
                    return None, "Cannot set event date to the past"
                
                # Tidak boleh mengubah tanggal event yang sedang ongoing atau completed
                if event.status in [EventStatus.ONGOING, EventStatus.COMPLETED]:
                    return None, "Cannot change date of ongoing or completed event"
            
            # Status transition validation
            if 'status' in data:
                new_status = EventStatus(data['status']) if isinstance(data['status'], str) else data['status']
                if not EventService._is_valid_status_transition(event.status, new_status):
                    return None, f"Invalid status transition from {event.status.value} to {new_status.value}"
            
            # Update event dengan data baru
            event.update_from_dict(data)
            
            # Save changes
            updated_event = EventRepository.update(event)
            return updated_event, None
            
        except ValueError as e:
            return None, f"Invalid data format: {str(e)}"
        except Exception as e:
            return None, f"Database error: {str(e)}"
    
    @staticmethod
    def delete_event(event_id: int) -> Tuple[bool, Optional[str]]:
        """
        Menghapus event dengan validasi business logic.
        
        Args:
            event_id (int): ID event yang akan dihapus
            
        Returns:
            Tuple[bool, Optional[str]]: (success, error_message)
        """
        # Cari event yang akan dihapus
        event = EventRepository.get_by_id(event_id)
        if not event:
            return False, "Event not found"
        
        # Business logic: tidak boleh menghapus event yang sedang ongoing
        if event.status == EventStatus.ONGOING:
            return False, "Cannot delete ongoing event"
        
        try:
            success = EventRepository.delete(event)
            return success, None
        except Exception as e:
            return False, f"Database error: {str(e)}"
    
    @staticmethod
    def search_events(query: str, page: int = 1, per_page: int = 20) -> Tuple[List[Event], Dict[str, Any]]:
        """
        Mencari event berdasarkan nama atau lokasi.
        
        Args:
            query (str): Query pencarian
            page (int): Nomor halaman
            per_page (int): Jumlah item per halaman
            
        Returns:
            Tuple[List[Event], Dict[str, Any]]: (events, pagination_info)
        """
        if not query or not query.strip():
            return [], {'page': page, 'per_page': per_page, 'total': 0, 'pages': 0, 'has_next': False, 'has_prev': False}
        
        # Validasi pagination parameters
        page = max(1, page)
        per_page = min(max(1, per_page), 100)
        
        return EventRepository.search(query.strip(), page, per_page)
    
    @staticmethod
    def _validate_event_data(data: Dict[str, Any], is_update: bool = False) -> Optional[str]:
        """
        Validasi data event.
        
        Args:
            data (Dict[str, Any]): Data yang akan divalidasi
            is_update (bool): Apakah ini untuk update operation
            
        Returns:
            Optional[str]: Error message jika ada error, None jika valid
        """
        if not is_update:
            # Required fields untuk create
            required_fields = ['name', 'event_date']
            for field in required_fields:
                if field not in data or not data[field]:
                    return f"Field '{field}' is required"
        
        # Validasi name
        if 'name' in data:
            name = data['name']
            if not isinstance(name, str) or len(name.strip()) == 0:
                return "Event name must be a non-empty string"
            if len(name) > 200:
                return "Event name must not exceed 200 characters"
        
        # Validasi location
        if 'location' in data and data['location']:
            location = data['location']
            if not isinstance(location, str):
                return "Location must be a string"
            if len(location) > 300:
                return "Location must not exceed 300 characters"
        
        # Validasi status
        if 'status' in data:
            status = data['status']
            if isinstance(status, str):
                try:
                    EventStatus(status)
                except ValueError:
                    valid_statuses = [s.value for s in EventStatus]
                    return f"Invalid status. Valid options: {', '.join(valid_statuses)}"
        
        return None
    
    @staticmethod
    def _is_valid_status_transition(current_status: EventStatus, new_status: EventStatus) -> bool:
        """
        Validasi transisi status event.
        
        Args:
            current_status (EventStatus): Status saat ini
            new_status (EventStatus): Status baru
            
        Returns:
            bool: True jika transisi valid
        """
        # Define valid transitions
        valid_transitions = {
            EventStatus.DRAFT: [EventStatus.PUBLISHED, EventStatus.CANCELLED],
            EventStatus.PUBLISHED: [EventStatus.ONGOING, EventStatus.CANCELLED],
            EventStatus.ONGOING: [EventStatus.COMPLETED, EventStatus.CANCELLED],
            EventStatus.COMPLETED: [],  # Final state
            EventStatus.CANCELLED: []   # Final state
        }
        
        return new_status in valid_transitions.get(current_status, [])
