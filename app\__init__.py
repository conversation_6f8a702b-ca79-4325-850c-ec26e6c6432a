"""
Event Management API - Application Factory

Implementasi Application Factory pattern untuk Flask app dengan modular blueprint architecture.
Mendukung konfigurasi environment-based dan integrasi dengan Supabase PostgreSQL.
"""

from flask import Flask
from flask_cors import CORS
from app.extensions import db
from app.config import Config


def create_app(config_class=Config):
    """
    Application Factory untuk membuat instance Flask app.
    
    Args:
        config_class: Kelas konfigurasi yang akan digunakan (default: Config)
        
    Returns:
        Flask: Instance aplikasi Flask yang sudah dikonfigurasi
    """
    # Inisialisasi Flask app
    app = Flask(__name__)
    
    # Load konfigurasi
    app.config.from_object(config_class)
    
    # Inisialisasi extensions
    db.init_app(app)
    
    # Setup CORS untuk frontend integration
    CORS(app, origins=app.config.get('CORS_ORIGINS', []))
    
    # Register blueprints
    from app.events.routes import events_bp
    app.register_blueprint(events_bp, url_prefix='/api/v1/events')
    
    # Health check endpoint
    @app.route('/health')
    def health_check():
        """Endpoint untuk health check aplikasi"""
        return {
            'status': 'healthy',
            'message': 'Event Management API is running',
            'version': app.config.get('API_VERSION', 'v1')
        }
    
    # Root endpoint dengan informasi API
    @app.route('/')
    def root():
        """Root endpoint dengan informasi dasar API"""
        return {
            'message': 'Event Management API',
            'version': app.config.get('API_VERSION', 'v1'),
            'endpoints': {
                'health': '/health',
                'events': '/api/v1/events'
            }
        }
    
    # Error handlers
    @app.errorhandler(404)
    def not_found(error):
        """Handler untuk 404 Not Found"""
        return {'error': 'Resource not found'}, 404
    
    @app.errorhandler(500)
    def internal_error(error):
        """Handler untuk 500 Internal Server Error"""
        return {'error': 'Internal server error'}, 500
    
    return app
