"""
Configuration Management untuk Event Management API

Mengelola konfigurasi aplikasi dengan environment variables untuk keamanan.
Mendukung berbagai environment (development, production, testing).
"""

import os
from dotenv import load_dotenv

# Load environment variables dari file .env
load_dotenv()


class Config:
    """
    Base configuration class dengan pengaturan umum aplikasi.
    Menggunakan environment variables untuk kredensial sensitif.
    """
    
    # Flask Configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    FLASK_ENV = os.environ.get('FLASK_ENV') or 'development'
    DEBUG = os.environ.get('FLASK_DEBUG', 'True').lower() in ['true', '1', 'yes']
    
    # Database Configuration
    # Supabase PostgreSQL connection string
    DATABASE_URL = os.environ.get('DATABASE_URL')
    if not DATABASE_URL:
        raise ValueError("DATABASE_URL environment variable is required")
    
    # SQLAlchemy Configuration
    SQLALCHEMY_DATABASE_URI = DATABASE_URL
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,  # Verify connections before use
        'pool_recycle': 300,    # Recycle connections every 5 minutes
    }
    
    # API Configuration
    API_VERSION = os.environ.get('API_VERSION', 'v1')
    
    # CORS Configuration
    CORS_ORIGINS = os.environ.get('CORS_ORIGINS', '').split(',') if os.environ.get('CORS_ORIGINS') else ['*']
    
    # Pagination defaults
    DEFAULT_PAGE_SIZE = 20
    MAX_PAGE_SIZE = 100


class DevelopmentConfig(Config):
    """Configuration untuk development environment"""
    DEBUG = True
    SQLALCHEMY_ECHO = True  # Log SQL queries untuk debugging


class ProductionConfig(Config):
    """Configuration untuk production environment"""
    DEBUG = False
    SQLALCHEMY_ECHO = False
    
    # Production-specific settings
    SQLALCHEMY_ENGINE_OPTIONS = {
        **Config.SQLALCHEMY_ENGINE_OPTIONS,
        'pool_size': 10,
        'max_overflow': 20,
    }


class TestingConfig(Config):
    """Configuration untuk testing environment"""
    TESTING = True
    DEBUG = True
    # Use in-memory SQLite untuk testing
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'


# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
