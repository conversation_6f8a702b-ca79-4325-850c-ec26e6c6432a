"""
Configuration Management untuk Event Management API

Mengelola konfigurasi aplikasi dengan environment variables untuk keamanan.
Menggunakan Supabase sebagai backend database dan authentication.
"""

import os
from dotenv import load_dotenv

# Load environment variables dari file .env
load_dotenv()


class Config:
    """
    Base configuration class dengan pengaturan umum aplikasi.
    Menggunakan environment variables untuk kredensial sensitif.
    """

    # Flask Configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    FLASK_ENV = os.environ.get('FLASK_ENV') or 'development'
    DEBUG = os.environ.get('FLASK_DEBUG', 'True').lower() in ['true', '1', 'yes']

    # Supabase Configuration
    SUPABASE_URL = os.environ.get('SUPABASE_URL')
    SUPABASE_KEY = os.environ.get('SUPABASE_KEY')

    if not SUPABASE_URL or not SUPABASE_KEY:
        raise ValueError("SUPABASE_URL and SUPABASE_KEY environment variables are required")

    # API Configuration
    API_VERSION = os.environ.get('API_VERSION', 'v1')

    # CORS Configuration
    CORS_ORIGINS = os.environ.get('CORS_ORIGINS', '').split(',') if os.environ.get('CORS_ORIGINS') else ['*']

    # Pagination defaults
    DEFAULT_PAGE_SIZE = 20
    MAX_PAGE_SIZE = 100


class DevelopmentConfig(Config):
    """Configuration untuk development environment"""
    DEBUG = True


class ProductionConfig(Config):
    """Configuration untuk production environment"""
    DEBUG = False


class TestingConfig(Config):
    """Configuration untuk testing environment"""
    TESTING = True
    DEBUG = True


# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
