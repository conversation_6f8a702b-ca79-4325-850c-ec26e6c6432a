-- Event Management API - Supabase Database Setup
-- Jalankan script ini di Supabase SQL Editor untuk membuat table dan indexes

-- Create events table
CREATE TABLE IF NOT EXISTS events (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    event_date TIMESTAMPTZ NOT NULL,
    location VARCHAR(300),
    status VARCHAR(20) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'ongoing', 'completed', 'cancelled')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- <PERSON>reate indexes untuk performance
CREATE INDEX IF NOT EXISTS idx_events_event_date ON events(event_date);
CREATE INDEX IF NOT EXISTS idx_events_status ON events(status);
CREATE INDEX IF NOT EXISTS idx_events_name ON events(name);

-- <PERSON><PERSON> function untuk auto-update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger untuk auto-update updated_at
DROP TRIGGER IF EXISTS update_events_updated_at ON events;
CREATE TRIGGER update_events_updated_at 
    BEFORE UPDATE ON events 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data untuk testing (optional)
INSERT INTO events (name, event_date, location, status) VALUES
('Tech Conference 2024', '2024-12-15 10:00:00+00', 'Jakarta Convention Center', 'published'),
('Workshop Python', '2024-11-20 14:00:00+00', 'Online', 'draft'),
('Startup Meetup', '2024-10-30 18:00:00+00', 'Bandung', 'ongoing')
ON CONFLICT (id) DO NOTHING;

-- Verify table creation
SELECT 
    table_name, 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'events' 
ORDER BY ordinal_position;
