"""
Event Routes - REST API Endpoints

Flask Blueprint untuk Event resource dengan RESTful endpoints.
Menangani HTTP requests, validasi input, dan formatting response JSON.
"""

from flask import Blueprint, request, jsonify
from app.events.service import EventService


# Create Blueprint untuk Event routes
events_bp = Blueprint('events', __name__)


@events_bp.route('', methods=['POST'])
def create_event():
    """
    POST /api/v1/events
    Membuat event baru.
    
    Request Body:
    {
        "name": "string (required)",
        "event_date": "ISO datetime string (required)",
        "location": "string (optional)",
        "status": "string (optional, default: draft)"
    }
    
    Returns:
        201: Event berhasil dibuat
        400: Bad request (validation error)
        500: Internal server error
    """
    try:
        # Ambil data JSON dari request
        data = request.get_json()
        
        if not data:
            return jsonify({
                'error': 'Request body must contain JSON data'
            }), 400
        
        # Panggil service untuk membuat event
        event, error = EventService.create_event(data)
        
        if error:
            return jsonify({
                'error': error
            }), 400
        
        # Return success response
        return jsonify({
            'message': 'Event created successfully',
            'data': event.to_dict()
        }), 201
        
    except Exception as e:
        return jsonify({
            'error': f'Internal server error: {str(e)}'
        }), 500


@events_bp.route('', methods=['GET'])
def get_events():
    """
    GET /api/v1/events
    Mengambil daftar event dengan pagination dan filter.
    
    Query Parameters:
        page (int): Nomor halaman (default: 1)
        per_page (int): Jumlah item per halaman (default: 20, max: 100)
        status (string): Filter berdasarkan status
        search (string): Pencarian berdasarkan nama atau lokasi
    
    Returns:
        200: Daftar event dengan metadata pagination
        500: Internal server error
    """
    try:
        # Ambil query parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status')
        search_query = request.args.get('search')
        
        # Jika ada search query, gunakan search function
        if search_query:
            events, pagination_info = EventService.search_events(search_query, page, per_page)
        else:
            events, pagination_info = EventService.get_all_events(page, per_page, status)
        
        # Convert events ke dictionary
        events_data = [event.to_dict() for event in events]
        
        return jsonify({
            'message': 'Events retrieved successfully',
            'data': events_data,
            'pagination': pagination_info
        }), 200
        
    except Exception as e:
        return jsonify({
            'error': f'Internal server error: {str(e)}'
        }), 500


@events_bp.route('/<int:event_id>', methods=['GET'])
def get_event(event_id):
    """
    GET /api/v1/events/{id}
    Mengambil event berdasarkan ID.
    
    Path Parameters:
        event_id (int): ID event
    
    Returns:
        200: Event data
        404: Event not found
        500: Internal server error
    """
    try:
        # Panggil service untuk mengambil event
        event = EventService.get_event_by_id(event_id)
        
        if not event:
            return jsonify({
                'error': 'Event not found'
            }), 404
        
        return jsonify({
            'message': 'Event retrieved successfully',
            'data': event.to_dict()
        }), 200
        
    except Exception as e:
        return jsonify({
            'error': f'Internal server error: {str(e)}'
        }), 500


@events_bp.route('/<int:event_id>', methods=['PUT'])
def update_event(event_id):
    """
    PUT /api/v1/events/{id}
    Update event berdasarkan ID.
    
    Path Parameters:
        event_id (int): ID event
    
    Request Body:
    {
        "name": "string (optional)",
        "event_date": "ISO datetime string (optional)",
        "location": "string (optional)",
        "status": "string (optional)"
    }
    
    Returns:
        200: Event berhasil diupdate
        400: Bad request (validation error)
        404: Event not found
        500: Internal server error
    """
    try:
        # Ambil data JSON dari request
        data = request.get_json()
        
        if not data:
            return jsonify({
                'error': 'Request body must contain JSON data'
            }), 400
        
        # Panggil service untuk update event
        event, error = EventService.update_event(event_id, data)
        
        if error:
            if error == "Event not found":
                return jsonify({
                    'error': error
                }), 404
            else:
                return jsonify({
                    'error': error
                }), 400
        
        return jsonify({
            'message': 'Event updated successfully',
            'data': event.to_dict()
        }), 200
        
    except Exception as e:
        return jsonify({
            'error': f'Internal server error: {str(e)}'
        }), 500


@events_bp.route('/<int:event_id>', methods=['DELETE'])
def delete_event(event_id):
    """
    DELETE /api/v1/events/{id}
    Menghapus event berdasarkan ID.
    
    Path Parameters:
        event_id (int): ID event
    
    Returns:
        200: Event berhasil dihapus
        400: Bad request (business logic error)
        404: Event not found
        500: Internal server error
    """
    try:
        # Panggil service untuk menghapus event
        success, error = EventService.delete_event(event_id)
        
        if error:
            if error == "Event not found":
                return jsonify({
                    'error': error
                }), 404
            else:
                return jsonify({
                    'error': error
                }), 400
        
        return jsonify({
            'message': 'Event deleted successfully'
        }), 200
        
    except Exception as e:
        return jsonify({
            'error': f'Internal server error: {str(e)}'
        }), 500


# Health check endpoint untuk events module
@events_bp.route('/health', methods=['GET'])
def events_health():
    """
    GET /api/v1/events/health
    Health check untuk events module.
    
    Returns:
        200: Module status
    """
    return jsonify({
        'module': 'events',
        'status': 'healthy',
        'endpoints': {
            'create': 'POST /api/v1/events',
            'list': 'GET /api/v1/events',
            'get': 'GET /api/v1/events/{id}',
            'update': 'PUT /api/v1/events/{id}',
            'delete': 'DELETE /api/v1/events/{id}',
            'search': 'GET /api/v1/events?search={query}'
        }
    }), 200
